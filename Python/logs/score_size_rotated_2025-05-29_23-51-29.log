2025-05-29 23:51:29 | INFO     | score:setup_logging:88 - Logging configured - Log file: /home/<USER>/repos/autolodge_retrained_deploy/Python/logs/score_2025-05-29_23-51-29.log
2025-05-29 23:51:29 | INFO     | score:initialize:539 - Starting initialization with local files...
2025-05-29 23:51:29 | INFO     | score:initialize:542 - Downloading NLTK stopwords...
2025-05-29 23:51:29 | INFO     | score:initialize:546 - Loading model from: /home/<USER>/repos/autolodge_retrained_deploy/Python/resources/ps-dev-claimsauto-tstarc.h5
2025-05-29 23:51:29 | INFO     | score:_load_model_from_path:476 - Loading Keras model from: /home/<USER>/repos/autolodge_retrained_deploy/Python/resources/ps-dev-claimsauto-tstarc.h5
2025-05-29 23:51:29 | INFO     | score:_load_model_from_path:478 - Keras model loaded successfully
2025-05-29 23:51:29 | INFO     | score:_load_preprocessing_components:500 - Loading tokenizer...
2025-05-29 23:51:29 | INFO     | score:_load_preprocessing_components:505 - Loading label encoder...
2025-05-29 23:51:29 | INFO     | score:_load_preprocessing_components:510 - Loading stopwords...
2025-05-29 23:51:29 | INFO     | score:_load_preprocessing_components:514 - Loading corpus...
2025-05-29 23:51:29 | INFO     | score:_load_preprocessing_components:519 - Initializing spell checker...
2025-05-29 23:51:35 | INFO     | score:_load_preprocessing_components:524 - All preprocessing components loaded successfully
2025-05-29 23:51:35 | INFO     | score:initialize:555 - Initialization completed successfully with local files
2025-05-29 23:51:35 | INFO     | score:run:885 - Starting inference...
2025-05-29 23:51:35 | INFO     | score:preprocess:650 - Starting preprocessing pipeline...
2025-05-29 23:51:35 | INFO     | score:preprocess:676 - Preprocessing completed for 1 items
2025-05-29 23:51:35 | INFO     | score:run:903 - Processing 1 items
2025-05-29 23:51:35 | INFO     | score:run:960 - Inference completed successfully for 1 items
