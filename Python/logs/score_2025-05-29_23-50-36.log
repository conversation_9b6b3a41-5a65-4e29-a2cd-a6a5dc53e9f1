2025-05-29 23:50:36 | INFO     | score:setup_logging:88 - Logging configured - Log file: /home/<USER>/repos/autolodge_retrained_deploy/Python/logs/score_2025-05-29_23-50-36.log
2025-05-29 23:50:36 | INFO     | score:initialize:538 - Starting initialization with local files...
2025-05-29 23:50:36 | INFO     | score:initialize:541 - Downloading NLTK stopwords...
2025-05-29 23:50:36 | INFO     | score:initialize:545 - Loading model from: resources/ps-dev-claimsauto-tstarc.h5
2025-05-29 23:50:36 | INFO     | score:_load_model_from_path:475 - Loading Keras model from: resources/ps-dev-claimsauto-tstarc.h5
2025-05-29 23:50:36 | INFO     | score:_load_model_from_path:477 - Keras model loaded successfully
2025-05-29 23:50:36 | INFO     | score:_load_preprocessing_components:499 - Loading tokenizer...
2025-05-29 23:50:36 | INFO     | score:_load_preprocessing_components:504 - Loading label encoder...
2025-05-29 23:50:36 | INFO     | score:_load_preprocessing_components:509 - Loading stopwords...
2025-05-29 23:50:36 | INFO     | score:_load_preprocessing_components:513 - Loading corpus...
2025-05-29 23:50:36 | INFO     | score:_load_preprocessing_components:518 - Initializing spell checker...
2025-05-29 23:50:42 | INFO     | score:_load_preprocessing_components:523 - All preprocessing components loaded successfully
2025-05-29 23:50:42 | INFO     | score:initialize:554 - Initialization completed successfully with local files
2025-05-29 23:50:42 | INFO     | score:run:884 - Starting inference...
2025-05-29 23:50:42 | INFO     | score:preprocess:649 - Starting preprocessing pipeline...
2025-05-29 23:50:42 | INFO     | score:preprocess:675 - Preprocessing completed for 2 items
2025-05-29 23:50:42 | INFO     | score:run:902 - Processing 2 items
2025-05-29 23:50:42 | INFO     | score:run:959 - Inference completed successfully for 2 items
