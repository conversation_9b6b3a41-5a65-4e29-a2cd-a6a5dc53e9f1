2025-05-29 23:27:17 | INFO     | Python.score:setup_logging:88 - Logging configured - Log file: /home/<USER>/repos/autolodge_retrained_deploy/Python/logs/score_2025-05-29_23-27-17.log
2025-05-29 23:27:17 | INFO     | Python.score:initialize:524 - Starting initialization with local files...
2025-05-29 23:27:17 | INFO     | Python.score:initialize:527 - Downloading NLTK stopwords...
2025-05-29 23:27:17 | INFO     | Python.score:initialize:531 - Loading model from: /home/<USER>/repos/autolodge_retrained_deploy/data/models/ps-dev-claimsauto-tstarc.h5
2025-05-29 23:27:17 | INFO     | Python.score:_load_model_from_path:469 - Loading Keras model from: /home/<USER>/repos/autolodge_retrained_deploy/data/models/ps-dev-claimsauto-tstarc.h5
2025-05-29 23:27:18 | INFO     | Python.score:_load_model_from_path:471 - Keras model loaded successfully
2025-05-29 23:27:18 | INFO     | Python.score:_load_preprocessing_components:490 - Loading tokenizer...
2025-05-29 23:27:18 | INFO     | Python.score:_load_preprocessing_components:495 - Loading label encoder...
2025-05-29 23:27:18 | INFO     | Python.score:_load_preprocessing_components:500 - Loading stopwords...
2025-05-29 23:27:18 | INFO     | Python.score:_load_preprocessing_components:504 - Loading corpus...
2025-05-29 23:27:18 | INFO     | Python.score:_load_preprocessing_components:509 - Initializing spell checker...
2025-05-29 23:27:23 | INFO     | Python.score:_load_preprocessing_components:514 - All preprocessing components loaded successfully
2025-05-29 23:27:23 | INFO     | Python.score:initialize:540 - Initialization completed successfully with local files
2025-05-29 23:27:23 | INFO     | Python.score:run:862 - Starting inference...
2025-05-29 23:27:23 | INFO     | Python.score:preprocess:627 - Starting preprocessing pipeline...
2025-05-29 23:27:23 | INFO     | Python.score:preprocess:653 - Preprocessing completed for 2 items
2025-05-29 23:27:23 | INFO     | Python.score:run:880 - Processing 2 items
2025-05-29 23:27:24 | INFO     | Python.score:run:937 - Inference completed successfully for 2 items
